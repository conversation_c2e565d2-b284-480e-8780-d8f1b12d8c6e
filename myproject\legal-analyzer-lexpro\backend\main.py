from fastapi import FastAPI, Form
import requests

app = FastAPI()

# Centralized function for calling LLM
def call_llm(prompt: str) -> str:
    try:
        response = requests.post(
            "http://localhost:11434/api/generate",
            json={"model": "llama2", "prompt": prompt, "stream": False},
            timeout=5
        )
        response.raise_for_status()  # Ensure we handle errors properly
        return response.json().get("response", "").strip()
    except requests.exceptions.RequestException:
        # Fallback mock response when Ollama is not available
        if "Summarize" in prompt:
            return "📄 **Mock Summary**: This is a sample legal document summary. The document appears to contain standard legal clauses including terms and conditions, liability limitations, and termination procedures. (Note: Ollama/Llama2 not running - this is a mock response)"
        elif "Extract key clauses" in prompt:
            return "📌 **Mock Key Clauses**: \n• Termination Clause: Standard 30-day notice\n• Liability Limitation: Limited to contract value\n• Payment Terms: Net 30 days\n• Confidentiality: 5-year non-disclosure\n(Note: Ollama/Llama2 not running - this is a mock response)"
        elif "Extract all named entities" in prompt:
            return "🔍 **Mock Named Entities**: \n• Parties: Company A, Company B\n• Locations: New York, California\n• Dates: January 1, 2024, December 31, 2024\n• Amounts: $100,000, $50,000\n(Note: Ollama/Llama2 not running - this is a mock response)"
        else:
            return "⚠️ Mock response: Ollama/Llama2 service not available. Please install and run Ollama with Llama2 model for actual AI analysis."

@app.post("/analyze/")
def analyze_legal(text: str = Form(...)):
    """Analyzes legal text for summaries, key clauses, and named entities."""
    prompts = {
        "summary": f"Summarize this legal document:\n\n{text}",
        "clauses": f"Extract key clauses from this legal text (e.g., Termination, Payment, Liability):\n\n{text}",
        "entities": f"Extract all named entities (e.g., parties, locations, dates):\n\n{text}"
    }

    # Ensuring LLM calls are safely executed
    results = {key: call_llm(prompt) for key, prompt in prompts.items()}
    return results