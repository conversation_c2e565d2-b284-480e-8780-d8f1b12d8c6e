alembic==1.15.2
altair==5.5.0
annotated-types==0.7.0
anyio==4.9.0
argon2-cffi==23.1.0
argon2-cffi-bindings==21.2.0
arrow==1.3.0
asttokens==3.0.0
async-lru==2.0.5
attrs==25.3.0
babel==2.17.0
bcrypt==4.3.0
beautifulsoup4==4.13.3
bleach==6.2.0
blinker==1.9.0
cachetools==5.5.2
certifi==2025.1.31
cffi==1.17.1
charset-normalizer==3.4.1
click==8.1.8
colorama==0.4.6
comm==0.2.2
cryptography==44.0.2
debugpy==1.8.13
decorator==5.2.1
defusedxml==0.7.1
ecdsa==0.19.1
executing==2.2.0
fastapi==0.115.12
fastjsonschema==2.21.1
filelock==3.18.0
fqdn==1.5.1
fsspec==2025.3.2
gitdb==4.0.12
GitPython==3.1.44
greenlet==3.2.1
h11==0.14.0
httpcore==1.0.7
httpx==0.28.1
idna==3.10
ipykernel==6.29.5
ipython==9.0.2
ipython_pygments_lexers==1.1.1
isoduration==20.11.0
jedi==0.19.2
Jinja2==3.1.6
json5==0.10.0
jsonpointer==3.0.0
jsonschema==4.23.0
jsonschema-specifications==2024.10.1
jupyter-events==0.12.0
jupyter-lsp==2.2.5
jupyter_client==8.6.3
jupyter_core==5.7.2
jupyter_server==2.15.0
jupyter_server_terminals==0.5.3
jupyterlab==4.3.6
jupyterlab_pygments==0.3.0
jupyterlab_server==2.27.3
llvmlite==0.44.0
Mako==1.3.10
MarkupSafe==3.0.2
matplotlib-inline==0.1.7
mistune==3.1.3
more-itertools==10.6.0
mpmath==1.3.0
narwhals==1.36.0
nbclient==0.10.2
nbconvert==7.16.6
nbformat==5.10.4
nest-asyncio==1.6.0
networkx==3.4.2
notebook==7.3.3
notebook_shim==0.2.4
numba==0.61.2
numpy==2.2.5
openai-whisper @ git+https://github.com/openai/whisper.git@517a43ecd132a2089d85f4ebc044728a71d49f6e
overrides==7.7.0
packaging==24.2
pandas==2.2.3
pandocfilters==1.5.1
parso==0.8.4
passlib==1.7.4
pillow==11.2.1
platformdirs==4.3.7
prometheus_client==0.21.1
prompt_toolkit==3.0.50
protobuf==5.29.4
psutil==7.0.0
psycopg2-binary==2.9.10
pure_eval==0.2.3
pyarrow==19.0.1
pyasn1==0.4.8
pycparser==2.22
pydantic==2.11.3
pydantic_core==2.33.1
pydeck==0.9.1
Pygments==2.19.1
pylance==0.25.2
python-dateutil==2.9.0.post0
python-jose==3.4.0
python-json-logger==3.3.0
python-multipart==0.0.20
pytz==2025.2
pywin32==310
pywinpty==2.0.15
PyYAML==6.0.2
pyzmq==26.3.0
referencing==0.36.2
regex==2024.11.6
requests==2.32.3
rfc3339-validator==0.1.4
rfc3986-validator==0.1.1
rpds-py==0.24.0
rsa==4.9.1
Send2Trash==1.8.3
setuptools==78.1.0
six==1.17.0
smmap==5.0.2
sniffio==1.3.1
soupsieve==2.6
SQLAlchemy==2.0.40
stack-data==0.6.3
starlette==0.46.2
streamlit==1.44.1
sympy==1.13.1
tenacity==9.1.2
terminado==0.18.1
tiktoken==0.9.0
tinycss2==1.4.0
toml==0.10.2
torch==2.6.0
tornado==6.4.2
tqdm==4.67.1
traitlets==5.14.3
types-python-dateutil==2.9.0.20241206
typing-inspection==0.4.0
typing_extensions==4.13.0
tzdata==2025.2
uri-template==1.3.0
urllib3==2.3.0
uvicorn==0.34.2
watchdog==6.0.0
wcwidth==0.2.13
webcolors==24.11.1
webencodings==0.5.1
websocket-client==1.8.0
