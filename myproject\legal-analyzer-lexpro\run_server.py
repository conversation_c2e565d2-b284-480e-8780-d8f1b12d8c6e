#!/usr/bin/env python3

import uvicorn
from backend.main import app

if __name__ == "__main__":
    print("🚀 Starting Legal Analyzer Backend Server...")
    print("📍 Server will be available at: http://127.0.0.1:8000")
    print("📖 API docs will be available at: http://127.0.0.1:8000/docs")
    print("⚠️  Note: Using mock responses since Ollama/Llama2 is not running")
    print("-" * 60)
    
    uvicorn.run(
        app, 
        host="127.0.0.1", 
        port=8000,
        log_level="info"
    )
