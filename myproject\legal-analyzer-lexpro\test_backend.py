#!/usr/bin/env python3

import sys
import traceback

try:
    print("Testing backend import...")
    from backend.main import app, call_llm
    print("✅ Backend imported successfully")

    print("Testing call_llm function...")
    result = call_llm("Summarize this test document")
    print(f"✅ call_llm result: {result[:100]}...")

    print("Testing FastAPI app...")
    print("✅ FastAPI app created successfully")

except Exception as e:
    print(f"❌ Error: {e}")
    print("Full traceback:")
    traceback.print_exc()
